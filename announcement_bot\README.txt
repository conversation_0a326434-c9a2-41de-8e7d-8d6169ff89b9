This bot reads announcements from webpages and filters them to their respective slack channel.
Announcement pages: Binance, Bybit.

Start:  python main.py

Structure:
    Core scripts:
        main.py – Runs the bot: scrapes, filters, checks cache, and posts to Slack.
        scraper.py – Scrapes Binance and Bybit announcements.
        filter.py – Flags important announcements based on keywords.
        slack_sender.py – Formats and sends messages to Slack channels.
        cache_utils.py – Handles cache to avoid reposting the same announcements.
        config.py – Stores URLs, Slack webhooks, and schedule interval, ...
    Others:
        requirements.txt – Python dependencies.
        last_announcement.json – Cached scraped data.
        bot.log – Logs of scraping and posting activity.
