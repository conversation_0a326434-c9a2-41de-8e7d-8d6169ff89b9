#!/usr/bin/env python3
"""
Main entry point for the Transaction Fee Analyzer.

This module runs the complete analysis pipeline:
1. Fetches transfer fee data from database
2. Performs advanced statistical analysis
3. Identifies high-impact optimization opportunities
4. Generates detailed Excel report with exchange comparisons
"""

from .db import get_transfer_fee_analysis, get_database_connection
from .focus_area import (
    calculate_advanced_fee_analysis,
    get_filtered_high_impact_transfers
)
from .comparisons import compare_exchanges
from .single_exchange import (
    get_main_assets_for_exchange_network,
    check_asset_dominance,
    get_networks_under_threshold
)
import pandas as pd
from datetime import datetime
import os
from typing import List

# Configuration constants
DEFAULT_ANALYSIS_DAYS = 14
DEFAULT_REFERENCE_DAYS = 90
DEFAULT_COMPETITORS = ['coinbase', 'kraken', 'binance']
NETWORK_THRESHOLD = 0.825
ASSET_THRESHOLD = 0.825
DOMINANCE_THRESHOLD = 0.95
MAX_ASSETS_DISPLAY = 5


def create_exchange_analysis_data(
    exchange: str,
    main_networks: List[str],
    df_reference: pd.DataFrame,
    competitors: List[str]
) -> List[List[str]]:
    """
    Create analysis data for a single exchange across all its main networks.
    """
    exchange_data = []
    
    for network_idx, network in enumerate(main_networks):
        print(f"  📈 Analyzing {exchange} on {network}...")
        
        try:
            # Run comparison analysis
            summary_df, comp_df = compare_exchanges(exchange, competitors, network=network)
            
            # Add network header
            exchange_data.append([f"=== {exchange.upper()} - {network} NETWORK ==="])
            exchange_data.append([''])  # Empty row
            
            # Add summary data
            exchange_data.append([f'SUMMARY COMPARISON ({network})'])
            summary_df_reset = summary_df.reset_index()
            summary_df_reset.columns = ['Metric'] + list(summary_df_reset.columns[1:])
            
            # Add column headers for day intervals
            headers = summary_df_reset.columns.tolist()
            # Convert numeric day intervals to "X day" format
            headers = [headers[0]] + [f"{col} day" if isinstance(col, (int, float)) else str(col) for col in headers[1:]]
            exchange_data.append(headers)
            
            for _, row in summary_df_reset.iterrows():
                exchange_data.append(row.tolist())
            
            exchange_data.append([''])  # Empty row
            
            # Add competitor details
            exchange_data.append(['COMPETITOR DETAILS'])
            comp_df_reset = comp_df.reset_index()
            comp_df_reset.columns = ['Competitor'] + list(comp_df_reset.columns[1:])
            
            # Add column headers for day intervals
            headers = comp_df_reset.columns.tolist()
            # Convert numeric day intervals to "X day" format
            headers = [headers[0]] + [f"{col} day" if isinstance(col, (int, float)) else str(col) for col in headers[1:]]
            exchange_data.append(headers)
            
            for _, row in comp_df_reset.iterrows():
                exchange_data.append(row.tolist())
            
            exchange_data.append([''])  # Empty row
            
            # Add main assets if not dominated
            if not check_asset_dominance(df_reference, exchange, network, DOMINANCE_THRESHOLD)[0]:
                main_assets = get_main_assets_for_exchange_network(
                    df_reference, exchange, network, ASSET_THRESHOLD
                )[:MAX_ASSETS_DISPLAY]
                exchange_data.append(['MAIN ASSETS'])
                exchange_data.append([', '.join(main_assets)])
            else:
                has_dominance, dominant_asset, dominance_pct = check_asset_dominance(
                    df_reference, exchange, network, DOMINANCE_THRESHOLD
                )
                exchange_data.append(['DOMINANT ASSET'])
                exchange_data.append([f"{dominant_asset} ({dominance_pct:.1%})"])
            
            # Add gap between networks
            if network_idx < len(main_networks) - 1:
                exchange_data.append([''])
                exchange_data.append([''])
                exchange_data.append([''])
            
            print(f"    ✓ Analysis completed for {exchange} on {network}")
            
        except Exception as e:
            print(f"    ❌ Error analyzing {exchange} on {network}: {e}")
            exchange_data.append([f"ERROR analyzing {exchange} on {network}: {str(e)}"])
            exchange_data.append([''])
    
    return exchange_data


def main():
    
    try:
        print("\n🔗 Establishing persistent database connection...")
        db = get_database_connection()
        print("✓ Database connection established and will persist throughout analysis")

        # 1. Fetch transfer fee data
        print(f"\n1. Fetching transfer fee data...")
        print(f"   📊 Analysis period: {DEFAULT_ANALYSIS_DAYS} days")
        print(f"   📊 Reference period: {DEFAULT_REFERENCE_DAYS} days")
        
        df_analysis = get_transfer_fee_analysis(days=DEFAULT_ANALYSIS_DAYS)
        df_reference = get_transfer_fee_analysis(days=DEFAULT_REFERENCE_DAYS)
        
        print(f"✓ Successfully retrieved {len(df_analysis)} analysis records")
        print(f"✓ Successfully retrieved {len(df_reference)} reference records")

        # 2. Prepare data for analysis
        print("\n2. Preparing data for statistical analysis...")
        df_adjusted = df_analysis.sort_values(by=['network', 'exchange_from'])
        df_adjusted = df_adjusted.groupby(['network', 'exchange_from']).agg({
            'transfer_count': 'sum',
            'total_fee_usdt': 'sum',
            'total_transfer_usdt': 'sum'
        }).reset_index()
        print("✓ Data preparation completed")

        # 3. Advanced statistical analysis
        print("\n3. Performing advanced statistical analysis...")
        enhanced_df = calculate_advanced_fee_analysis(df_adjusted)
        enhanced_df = enhanced_df.sort_values(by=['network', 'total_fee_usdt'], ascending=[True, False])
        print(f"✓ Enhanced analysis completed with {len(enhanced_df.columns)} total columns")

        # 4. Filter high-impact opportunities
        print("\n4. Filtering high-impact opportunities...")
        filtered_df = get_filtered_high_impact_transfers(enhanced_df)
        print(f"✓ Filtered {len(filtered_df)} high-impact opportunities")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        output_dir = "Outputs"
        os.makedirs(output_dir, exist_ok=True)
        
        excel_filename = os.path.join(output_dir, f"fee_analysis_report_{timestamp}.xlsx")
        
        print(f"\n📊 Creating Excel report: {excel_filename}")
        
        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            # Write opportunities to first sheet
            filtered_df.to_excel(writer, sheet_name='High Impact Opportunities', index=False)
            print(f"✓ Written {len(filtered_df)} opportunities to Excel")

            # Get unique exchanges from filtered opportunities
            unique_exchanges = filtered_df['exchange_from'].unique()
            print(f"✓ Found {len(unique_exchanges)} unique exchanges to analyze")

            # 5. Create detailed analysis sheets for each exchange
            for exchange in unique_exchanges:
                print(f"\n🔍 Analyzing exchange: {exchange}")
                
                # Get main networks for this exchange
                main_networks = get_networks_under_threshold(
                    df_reference, exchange, NETWORK_THRESHOLD
                )
                print(f"✓ Found {len(main_networks)} main networks for {exchange}")
                
                # Create analysis data
                exchange_data = create_exchange_analysis_data(
                    exchange, main_networks, df_reference, DEFAULT_COMPETITORS
                )
                
                # Write to Excel sheet
                if exchange_data:
                    sheet_name = f"{exchange}_analysis"[:31]  # Excel sheet name limit
                    exchange_df = pd.DataFrame(exchange_data)
                    exchange_df.to_excel(writer, sheet_name=sheet_name, index=False, header=False)
                    print(f"✓ Written {exchange} analysis to Excel sheet")

        print(f"\n✅ Excel report created successfully: {excel_filename}")
        print(f"📁 File location: {os.path.abspath(excel_filename)}")
        print(f"📊 Report contains {len(filtered_df)} high-impact opportunities")
        print(f"🏢 Analyzed {len(unique_exchanges)} exchanges across their main networks")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit(main())