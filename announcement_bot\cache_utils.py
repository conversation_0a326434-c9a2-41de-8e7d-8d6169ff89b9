from typing import Dict, Any
import os
import json
import yaml

with open('config.yml', 'r') as file:
    config = yaml.safe_load(file)

CACHE_FILE = config['CACHE_FILE']

def load_last_sent_announcement() -> Dict[str, Any]:
    if not os.path.exists(CACHE_FILE):
        return {}
    try:
        with open(CACHE_FILE, 'r') as f:
            content = f.read().strip()
            if not content:
                return {}
            return json.loads(content)
    except Exception:
        return {}

def save_last_sent_announcement(source: str, announcement: Dict[str, Any]) -> None:
    cache = load_last_sent_announcement()
    cache[source] = announcement
    with open(CACHE_FILE, 'w') as f:
        json.dump(cache, f, indent=2)
