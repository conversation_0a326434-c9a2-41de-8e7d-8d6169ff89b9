#!/usr/bin/env python3
"""
Script to remove all whitelisted addresses from Bitso
WARNING: This will delete ALL contacts from your Bitso account!
"""
import sys
import logging
import time
import json
from typing import List
from bitso_whitelister import BitsoRequester, load_config, Address

# Global logger
log = None

def setup_logging():
    """Set up logging"""
    global log
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    log = logging.getLogger(__name__)
    return log

def confirm_deletion():
    """Ask user for confirmation before deleting all contacts"""
    print("\n" + "="*70)
    print("🚨 WARNING: DESTRUCTIVE OPERATION 🚨")
    print("="*70)
    print("This script will DELETE ALL whitelisted addresses from your Bitso account!")
    print("This action is IRREVERSIBLE and will remove all your saved contacts.")
    print("="*70)
    
    # First confirmation
    response1 = input("\nAre you sure you want to proceed? (type 'yes' to continue): ").strip().lower()
    if response1 != 'yes':
        print("Operation cancelled.")
        return False
    
    # Second confirmation with random check
    import random
    check_number = random.randint(1000, 9999)
    print(f"\nFinal confirmation required!")
    print(f"Type the number {check_number} to confirm deletion:")
    
    try:
        response2 = int(input().strip())
        if response2 != check_number:
            print("Incorrect confirmation number. Operation cancelled.")
            return False
    except ValueError:
        print("Invalid input. Operation cancelled.")
        return False
    
    print("\n✅ Confirmation received. Proceeding with deletion...")
    return True

class BitsoContactRemover(BitsoRequester):
    """Extended BitsoRequester with contact deletion capabilities"""
    
    def delete_contact(self, contact_id: str) -> bool:
        """Delete a specific contact by ID"""
        try:
            # Using the consumer-contacts endpoint with DELETE method
            response = self._make_authenticated_request("DELETE", f"/consumer-contacts/{contact_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success", False):
                    return True
                else:
                    log.error(f"Failed to delete contact {contact_id}: {data}")
                    return False
            else:
                log.error(f"HTTP error deleting contact {contact_id}: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            log.error(f"Exception deleting contact {contact_id}: {e}")
            return False
    
    def remove_all_contacts(self, dry_run: bool = True) -> dict:
        """Remove all contacts from Bitso account"""
        log.info("Starting contact removal process...")
        
        # Get all existing contacts
        contacts = self.get_contacts()
        if not contacts:
            log.info("No contacts found to delete.")
            return {"total": 0, "deleted": 0, "failed": 0}
        
        log.info(f"Found {len(contacts)} contacts to delete")
        
        # Show what will be deleted
        print(f"\n📋 Contacts to be deleted:")
        print("-" * 80)
        for i, contact in enumerate(contacts[:10], 1):  # Show first 10
            print(f"{i:2d}. {contact.remark} ({contact.coin}/{contact.chain_type})")
            print(f"    Address: {contact.address[:40]}{'...' if len(contact.address) > 40 else ''}")
            if contact.memo:
                print(f"    Memo: {contact.memo}")
            print()
        
        if len(contacts) > 10:
            print(f"... and {len(contacts) - 10} more contacts")
        print("-" * 80)
        
        if dry_run:
            log.info("DRY RUN: No contacts will actually be deleted.")
            return {"total": len(contacts), "deleted": 0, "failed": 0}
        
        # Get contact IDs from the API response
        log.info("Fetching contact IDs from API...")
        try:
            response = self._make_authenticated_request("GET", "/consumer-contacts")
            if response.status_code != 200:
                log.error(f"Failed to get contact details: {response.status_code}")
                return {"total": len(contacts), "deleted": 0, "failed": len(contacts)}
            
            data = response.json()
            if not data.get("success", False):
                log.error(f"API returned error: {data}")
                return {"total": len(contacts), "deleted": 0, "failed": len(contacts)}
            
            contact_ids = []
            contact_lookup = {}
            for contact in data.get("payload", []):
                contact_id = contact.get("contact_id")
                alias = contact.get("alias", "Unknown")
                if contact_id:
                    contact_ids.append(contact_id)
                    contact_lookup[contact_id] = alias
            
            log.info(f"Found {len(contact_ids)} contact IDs to delete")
            
        except Exception as e:
            log.error(f"Error fetching contact IDs: {e}")
            return {"total": len(contacts), "deleted": 0, "failed": len(contacts)}
        
        # Delete each contact
        deleted_count = 0
        failed_count = 0
        
        for i, contact_id in enumerate(contact_ids, 1):
            alias = contact_lookup.get(contact_id, f"ID:{contact_id}")
            log.info(f"[{i}/{len(contact_ids)}] Deleting contact: {alias}")
            
            if self.delete_contact(contact_id):
                log.info(f"✅ Successfully deleted: {alias}")
                deleted_count += 1
            else:
                log.error(f"❌ Failed to delete: {alias}")
                failed_count += 1
            
            # Rate limiting - be gentle with the API
            time.sleep(1.0)
        
        return {
            "total": len(contact_ids),
            "deleted": deleted_count,
            "failed": failed_count
        }

def main():
    """Main function"""
    log = setup_logging()
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Remove all whitelisted addresses from Bitso")
    parser.add_argument("-c", "--config", required=True, help="Configuration file path")
    parser.add_argument("--dry-run", action="store_true", 
                       help="Show what would be deleted without actually deleting (default)")
    parser.add_argument("--live", action="store_true", 
                       help="Actually delete the contacts (USE WITH EXTREME CAUTION)")
    parser.add_argument("--force", action="store_true",
                       help="Skip confirmation prompts (dangerous!)")
    
    args = parser.parse_args()
    
    # Determine if this is a real deletion
    # If --live is specified, it's live mode regardless of --dry-run default
    # If --dry-run is explicitly specified, it's dry-run mode
    # If neither is specified, default to dry-run mode
    is_live = args.live and not args.dry_run
    if args.live:
        is_live = True  # --live flag overrides default dry-run behavior
    
    try:
        # Load configuration
        config = load_config(args.config)
        
        # Create remover instance
        remover = BitsoContactRemover(config)
        
        # Safety checks
        if is_live:
            if not args.force and not confirm_deletion():
                log.info("Operation cancelled by user.")
                return
            
            log.warning("🔥 LIVE MODE: Contacts will be permanently deleted!")
        else:
            log.info("🔍 DRY RUN MODE: Showing what would be deleted")
        
        # Execute removal
        results = remover.remove_all_contacts(dry_run=not is_live)
        
        # Summary
        print(f"\n{'='*60}")
        print("📊 REMOVAL SUMMARY")
        print(f"{'='*60}")
        print(f"Total contacts found: {results['total']}")
        print(f"Successfully deleted: {results['deleted']}")
        print(f"Failed to delete: {results['failed']}")
        
        if is_live:
            if results['failed'] == 0:
                print("🎉 All contacts successfully deleted!")
            else:
                print(f"⚠️  {results['failed']} contacts could not be deleted.")
        else:
            print("\n💡 To actually delete contacts, run with --live flag")
            print("   Example: python remove_all_whitelist.py -c config.yml --live")
        
        print(f"{'='*60}")
        
    except Exception as e:
        log.error(f"Script failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()