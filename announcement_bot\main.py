# main.py
from filter import is_important_announcement
from slack_sender import send_to_slack, format_message
from scraper import get_all_announcements
from cache_utils import load_last_sent_announcement, save_last_sent_announcement
import logging
import yaml
from typing import Dict, List, Any

with open('config.yml', 'r') as file:
    config = yaml.safe_load(file)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler("bot.log"),
        logging.StreamHandler()
    ]
)

def post_scrape_summary(all_announcements: Dict[str, List[Dict[str, Any]]], new_announcements: List[Dict[str, Any]]) -> None:
    message = "Scrape Summary:"
    for source, anns in all_announcements.items():
        message += f" {source}: {len(anns)},"
    message += f" New announcements: {len(new_announcements)}"
    logging.info(message)

    if len(all_announcements["Binance"]) < config['EXPECTED_COUNT']['binance'] or len(all_announcements["Bybit"]) < config['EXPECTED_COUNT']['bybit']:
        alert = "<!channel> bot could not scrape any new announcements on Binance/Bybit"
        send_to_slack(config['SLACK_WEBHOOKS']['bot_status'], alert)
        send_to_slack(config['SLACK_WEBHOOKS']['bot_status'], message)
        logging.info(alert)

def process_site_announcements(source: str, announcements: List[Dict[str, Any]], cache: Dict[str, Any]) -> List[Dict[str, Any]]:
    new_anns = []
    last_seen_url = cache.get(source, {}).get('url')
    for ann in announcements:
        if ann['url'] == last_seen_url:
            break

        if is_important_announcement(ann["title"]):
            webhook = config['SLACK_WEBHOOKS']['important']
        else:
            webhook = config['SLACK_WEBHOOKS']['others']

        message = format_message(ann)
        send_to_slack(webhook, message)

        logging.info(f"Posted: {ann['title']}")

        new_anns.append(ann)

    if new_anns:
        save_last_sent_announcement(source, new_anns[0])

    return new_anns

def process_announcements() -> None:
    logging.info("Checking for new announcements...")
    all_announcements = get_all_announcements()
    cache = load_last_sent_announcement()
    new_total = []

    for source, announcements in all_announcements.items():
        new = process_site_announcements(source, announcements, cache)
        new_total.extend(new)

    post_scrape_summary(all_announcements, new_total)

if __name__ == "__main__":
    process_announcements()
