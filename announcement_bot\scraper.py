from typing import List, Dict, Any
import requests
import yaml
from datetime import datetime
from pybit.unified_trading import HTTP

with open('config.yml', 'r') as file:
    config = yaml.safe_load(file)

def scrape_binance() -> List[Dict[str, Any]]:
    results = []
    try:
        url = config['URL']['binance']
        response = requests.get(url)
        data = response.json()
        for catalog in data.get("data", {}).get("catalogs", []):
            for article in catalog.get("articles", []):
                title = article.get("title")
                url = config['URL']['binancefw'] + article.get('code')
                timestamp = article.get("releaseDate", 0)
                date = datetime.utcfromtimestamp(timestamp / 1000).strftime('%Y-%m-%d')
                results.append({
                    "source": "Binance",
                    "title": title,
                    "url": url,
                    "date": date,
                    "timestamp": timestamp
                })
    except Exception:
        print ("Failed to load binance")

    results.sort(key=lambda x: x["timestamp"], reverse=True)
    for item in results:
        item.pop("timestamp")

    return results

def scrape_bybit(limit: int = 100) -> List[Dict[str, Any]]:
    results = []
    session = HTTP(testnet=False)
    try:
        response = session.get_announcement(locale="en-US", limit=limit)
    except Exception as e:
        print(f"API call failed: {e}")
        return results

    if response.get("retCode") != 0:
        print(f"API returned error: {response.get('retMsg')}")
        return results

    articles = response.get("result", {}).get("list", [])
    for article in articles:
        title = article.get("title", "No Title")
        url = article.get("url", "")
        timestamp = article.get("dateTimestamp", 0)
        date = datetime.utcfromtimestamp(timestamp / 1000).strftime("%Y-%m-%d %H:%M:%S")
        results.append({
            "source": "Bybit",
            "title": title,
            "url": url,
            "date": date,
            "timestamp": timestamp,
        })

    results.sort(key=lambda x: x["timestamp"], reverse=True)
    for item in results:
        item.pop("timestamp")

    return results

def get_all_announcements() -> Dict[str, List[Dict[str, Any]]]:
    return {
        "Binance": scrape_binance(),
        "Bybit": scrape_bybit()
    }
