## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd transaction-fee-analyzer
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure database connection**
   - Copy `config.yml` to `config.local.yml`
   - Update with your PostgreSQL credentials:
   ```yml
   database:
     user: your_username
     password: your_password
     database: your_database
     host: your_host
     port: 5432
   ```

## 🚦 Usage

### Basic Analysis
```bash
python -m analyzer
```

### Configuration Options
Edit the constants in `analyzer/__main__.py`:
- `DEFAULT_ANALYSIS_DAYS`: Period for identifying opportunities (default: 10 days)
- `DEFAULT_REFERENCE_DAYS`: Reference period for comparisons (default: 90 days)
- `DEFAULT_COMPETITORS`: List of benchmark exchanges
- `NETWORK_THRESHOLD`: Threshold for main networks (default: 0.825)
- `ASSET_THRESHOLD`: Threshold for main assets (default: 0.825)
- `DOMINANCE_THRESHOLD`: Threshold for asset dominance (default: 0.95)

### Individual Module Testing
```bash
# Test database connection
python -m analyzer.db

# Test focus area analysis
python -m analyzer.focus_area

# Test single exchange analysis
python -m analyzer.single_exchange
```

## 📈 Output

The analyzer generates a timestamped Excel file with multiple sheets:

### Sheet 1: High Impact Opportunities
- Complete list of identified optimization opportunities
- Network, exchange, fees, scoring, and potential savings
- Sorted by potential savings (descending)

### Exchange Analysis Sheets
For each exchange with opportunities:
- **Summary Comparison**: Primary vs. competitor fee analysis
- **Competitor Details**: Detailed breakdown of competitor fees
- **Main Assets**: Top 5 assets by fee volume (if not dominated)
- **Dominant Assets**: Single asset info if >95% dominance
- **Multi-Network**: Stacked analysis for all main networks

## 🗂️ Project Structure

```
analyzer/
├── __main__.py          # Main application entry point
├── db.py               # Database connection and query management
├── focus_area.py       # Advanced statistical analysis
├── comparisons.py      # Exchange comparison functionality
└── single_exchange.py  # Single exchange analysis utilities
```

## 🔧 Configuration Files

- `config.template.yml`: Template configuration file
- `config.local.yml`: Local database credentials (gitignored)
- `requirements.txt`: Python dependencies

## 🗄️ Database Schema

The application queries the following tables:
- `capman.transfers`: Transfer transaction data
- `capman.sources_map`: Exchange mapping data


## 📋 TODO & Future Improvements

- [ ] **CLI Interface**: Add command-line arguments for common parameters
- [ ] **Performance Optimization**: Implement database query caching
- [ ] **Deeper analyses for assets**: Add asset level analyses involving blockchain fee comaprison with UI fees

