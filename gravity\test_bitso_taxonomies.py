#!/usr/bin/env python3
"""
Test script to understand Bitso's expected taxonomies
"""

import sys
import logging
from bitso_whitelister import BitsoRequester, load_config
from argparse import ArgumentParser

def main():
    parser = ArgumentParser(__file__)
    parser.add_argument("-c", "--config", required=True, help="Config file path", dest="config_file")
    args = parser.parse_args()
    
    # Set up logging
    log = logging.getLogger()
    log.setLevel(logging.DEBUG)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    log.addHandler(handler)
    
    config = load_config(args.config_file)
    bitso = BitsoRequester(config)
    
    # Test getting withdrawal methods for different currencies
    currencies = ["btc", "eth", "xrp", "usd"]
    for currency in currencies:
        log.info(f"Getting withdrawal methods for {currency}")
        methods = bitso.get_withdrawal_methods(currency)
        log.info(f"{currency} methods: {methods}")
        print(f"\n{currency.upper()} withdrawal methods:")
        print("=" * 50)
        if isinstance(methods, dict):
            for key, value in methods.items():
                print(f"{key}: {value}")
        else:
            print(methods)
        print("\n")

if __name__ == "__main__":
    main()
