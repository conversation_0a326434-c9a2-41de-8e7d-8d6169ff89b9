import logging
import sys
import json
import requests
import time
import hmac
import hashlib
import base64
from typing import Dict, Optional, List
from dataclasses import dataclass
from argparse import ArgumentParser
from csv import writer, DictReader
import yaml


log = logging.getLogger()


@dataclass
class Address:
    remark: str
    coin: str
    chain_type: str
    address: str
    memo: str


# Network name mappings for USDC chain identification
USDC_CHAIN_MAPPING = {
    "ARBITRUM": "ARB",
    "OPTIMISM": "OP",
    "ETH": "ETH",
    "ETHEREUM": "ETH",
    "POLYGON": "POLY",
    "MATIC": "POLY",
    "STELLAR": "xlm",
    "XLM": "xlm",
    "SOLANA": "SOL",
    "SOL": "SOL",
}


class BitsoRequester:
    def __init__(self, config: Dict):
        self.base_url = config["base_url"]
        self.api_key = config["api_key"]
        self.api_secret = config["api_secret"]
        self.beneficiary_name = config["beneficiary_name"]
        self.beneficiary_type = config["beneficiary_type"]

        # Load network mapping from config
        self.network_map = {}
        if "BYBIT_TO_BITSO_NETWORK_MAP" in config:
            for mapping in config["BYBIT_TO_BITSO_NETWORK_MAP"]:
                self.network_map[mapping["key"]] = mapping["value"]

        # Cache for discovered taxonomies
        self._taxonomy_cache = {}
        self._withdrawal_methods_cache = {}

        self.default_headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "user-agent": "BitsoWhitelister/1.0",
        }

    def _get_withdrawal_methods_cached(self, currency: str) -> List[Dict]:
        """Get withdrawal methods with caching"""
        if currency not in self._withdrawal_methods_cache:
            methods = self.get_withdrawal_methods(currency)
            if isinstance(methods, list):
                self._withdrawal_methods_cache[currency] = methods
            else:
                self._withdrawal_methods_cache[currency] = []
        return self._withdrawal_methods_cache[currency]

    def _discover_taxonomy_for_address(self, address: Address) -> Dict:
        """Procedurally discover the correct taxonomy for an address"""
        cache_key = f"{address.coin}_{address.chain_type}"

        if cache_key in self._taxonomy_cache:
            return self._taxonomy_cache[cache_key].copy()

        log.debug(f"Discovering taxonomy for {address.coin} on {address.chain_type}")

        # Map coin to Bitso currency
        currency_map = {
            "BTC": "btc",
            "ETH": "eth",
            "XRP": "xrp",
            "USDC": "usd",
            "USD": "usd",
            "USDT": "usdt"
        }

        bitso_currency = currency_map.get(address.coin.upper(), address.coin.lower())

        # Get withdrawal methods for this currency
        methods = self._get_withdrawal_methods_cached(bitso_currency)

        # Find the best matching method
        taxonomy = self._find_matching_taxonomy(address, methods)

        if taxonomy:
            self._taxonomy_cache[cache_key] = taxonomy.copy()
            log.debug(f"Discovered taxonomy for {cache_key}: {taxonomy}")
        else:
            log.warning(f"Could not discover taxonomy for {cache_key}, using fallback")
            taxonomy = self._get_fallback_taxonomy(address)

        return taxonomy

    def _find_matching_taxonomy(self, address: Address, methods: List[Dict]) -> Optional[Dict]:
        """Find the best matching taxonomy from withdrawal methods"""

        # For USDC, look for Circle Transfer method first
        if address.coin.upper() in ["USDC", "USD"]:
            # Look for Circle Transfer method
            for method in methods:
                if method.get("method") == "usdc_trf" and method.get("network") == "circle":
                    taxonomy = {
                        "network": method["network"],
                        "protocol": method["protocol"],
                        "asset": method.get("asset", "usdc"),
                        "currency": method.get("currency", "usd"),
                        "integration": method.get("integration", "circle-api")
                    }

                    # Add chain for USDC
                    chain = USDC_CHAIN_MAPPING.get(address.chain_type.upper())
                    if chain:
                        taxonomy["chain"] = chain

                    return taxonomy

            # Fallback to network-specific USDC methods
            network_method_map = {
                "ETH": "eth_erc20",
                "ETHEREUM": "eth_erc20",
                "ARBITRUM": "arb_erc20",
                "OPTIMISM": "opt_erc20",
                "POLYGON": "pol_erc20",
                "MATIC": "pol_erc20",
                "SOLANA": "sol_spl",
                "SOL": "sol_spl"
            }

            target_method = network_method_map.get(address.chain_type.upper())
            if target_method:
                for method in methods:
                    if method.get("method") == target_method:
                        return {
                            "network": method["network"],
                            "protocol": method["protocol"],
                            "asset": method.get("asset", "usdc"),
                            "currency": method.get("currency", "usd"),
                            "integration": method.get("integration", "fblocks-v1")
                        }

        # For all other coins, try multiple matching strategies
        else:
            coin_upper = address.coin.upper()
            coin_lower = address.coin.lower()
            
            # Strategy 1: Direct method name match (most common)
            for method in methods:
                method_name = method.get("method", "")
                
                # Skip Bitso Transfer method
                if method_name == "bt":
                    continue
                
                # Direct coin match (btc, eth, xrp, etc.)
                if method_name == coin_lower:
                    return self._build_taxonomy_from_method(method, address)
                
                # Compound method names (eth_erc20, trx_trc20, etc.)
                if coin_lower in method_name:
                    return self._build_taxonomy_from_method(method, address)
            
            # Strategy 2: Check if it's an ERC-20 token on Ethereum
            if address.chain_type.upper() in ["ETH", "ETHEREUM"]:
                for method in methods:
                    if method.get("method") == "eth_erc20":
                        return self._build_taxonomy_from_method(method, address)
            
            # Strategy 3: Try common network mappings
            network_method_patterns = {
                "TRX": ["trx_trc20", "tron"],
                "TRON": ["trx_trc20", "tron"],
                "SOL": ["sol_spl", "solana"],
                "SOLANA": ["sol_spl", "solana"],
                "POLYGON": ["pol_erc20", "polygon"],
                "MATIC": ["pol_erc20", "polygon"],
                "ARBITRUM": ["arb_erc20", "arbitrum"],
                "OPTIMISM": ["opt_erc20", "optimism"]
            }
            
            chain_patterns = network_method_patterns.get(address.chain_type.upper(), [])
            for pattern in chain_patterns:
                for method in methods:
                    if pattern in method.get("method", ""):
                        return self._build_taxonomy_from_method(method, address)

        return None

    def _build_taxonomy_from_method(self, method: Dict, address: Address) -> Dict:
        """Build taxonomy from API method response"""
        return {
            "network": method.get("network"),
            "protocol": method.get("protocol"),
            "asset": method.get("asset", address.coin.lower()),
            "currency": method.get("currency", address.coin.lower()),
            "integration": method.get("integration", "fblocks-v1")
        }

    def _get_fallback_taxonomy(self, address: Address) -> Dict:
        """Get fallback taxonomy when discovery fails"""
        log.warning(f"Using fallback taxonomy for {address.coin}/{address.chain_type}")

        coin_upper = address.coin.upper()
        chain_upper = address.chain_type.upper()
        
        # Try to get accurate network from config map first
        config_network = self.network_map.get(coin_upper) or self.network_map.get(chain_upper)
        
        # Special case taxonomies based on API analysis
        if coin_upper == "BTC":
            return {"network": "btc", "protocol": "btc", "asset": "btc", "currency": "btc", "integration": "bitgo-v2"}
        elif coin_upper == "ETH":
            return {"network": "eth", "protocol": "eth", "asset": "eth", "currency": "eth", "integration": "fblocks-v1"}
        elif coin_upper == "XRP":
            # Use correct protocol from API analysis
            return {"network": "rp", "protocol": "ripple", "asset": "xrp", "currency": "xrp", "integration": "rippled"}
        elif coin_upper == "BCH":
            # Use correct integration from API analysis
            return {"network": "bch", "protocol": "bch", "asset": "bch", "currency": "bch", "integration": "bitgo-v2"}
        elif coin_upper == "HBAR":
            # Use config map network (hedera)
            network = config_network or "hedera"
            return {"network": network, "protocol": "hbar", "asset": "hbar", "currency": "hbar", "integration": "fblocks-v1"}
        elif coin_upper == "XLM":
            # Use config map network (stellar)
            network = config_network or "stellar"
            return {"network": network, "protocol": "xlm", "asset": "xlm", "currency": "xlm", "integration": "fblocks-v1"}
        elif coin_upper == "NEAR":
            # Use config map network (near)
            network = config_network or "near"
            return {"network": network, "protocol": "near", "asset": "near", "currency": "near", "integration": "fblocks-v1"}
        elif coin_upper in ["USDC", "USD"]:
            taxonomy = {"network": "circle", "protocol": "usdc_trf", "asset": "usdc", "currency": "usd", "integration": "circle-api"}
            chain = USDC_CHAIN_MAPPING.get(chain_upper, "ETH")
            taxonomy["chain"] = chain
            return taxonomy
        # ERC-20 tokens on Ethereum
        elif chain_upper in ["ETH", "ETHEREUM"]:
            return {
                "network": "eth",
                "protocol": "erc20",  # Key fix for ERC-20 tokens
                "asset": address.coin.lower(),
                "currency": address.coin.lower(),
                "integration": "fblocks-v1"
            }
        else:
            # Generic fallback using config map if available
            network = config_network or address.chain_type.lower()
            return {
                "network": network,
                "protocol": address.chain_type.lower(),
                "asset": address.coin.lower(),
                "currency": address.coin.lower(),
                "integration": "fblocks-v1"
            }

    def _create_signature(self, nonce: str, method: str, request_path: str, body: str = "") -> str:
        """Create HMAC signature for Bitso API authentication"""
        message = nonce + method + request_path + body
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return signature

    def _make_authenticated_request(self, method: str, endpoint: str, data: Dict = None) -> requests.Response:
        """Make authenticated request to Bitso API"""
        nonce = str(int(time.time() * 1000))
        request_path = f"/api/v3{endpoint}"
        body = json.dumps(data) if data else ""
        
        signature = self._create_signature(nonce, method, request_path, body)
        
        headers = self.default_headers.copy()
        headers.update({
            "Authorization": f"Bitso {self.api_key}:{nonce}:{signature}",
        })
        
        url = f"{self.base_url}{request_path}"
        
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, data=body)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers, data=body)
        else:
            raise ValueError(f"Unsupported method: {method}")
            
        return response

    def get_withdrawal_methods(self, currency: str) -> Dict:
        """Get withdrawal methods for a currency to understand taxonomy"""
        try:
            response = self._make_authenticated_request("GET", f"/withdrawal_methods/{currency}")
            log.debug(f"Get withdrawal methods for {currency} response: {response.text}")

            if response.status_code != 200:
                log.error(f"Failed to get withdrawal methods: {response.status_code} - {response.text}")
                return {}

            data = response.json()
            if not data.get("success", False):
                log.error(f"API returned error: {data}")
                return {}

            return data.get("payload", {})

        except Exception as e:
            log.error(f"Error getting withdrawal methods: {e}")
            return {}

    def get_contacts(self) -> List[Address]:
        """Get existing contacts/whitelist from Bitso"""
        try:
            response = self._make_authenticated_request("GET", "/consumer-contacts")
            log.debug(f"Get contacts response: {response.text}")
            
            if response.status_code != 200:
                log.error(f"Failed to get contacts: {response.status_code} - {response.text}")
                return []
                
            data = response.json()
            if not data.get("success", False):
                log.error(f"API returned error: {data}")
                return []
                
            contacts = []
            for contact in data.get("payload", []):
                # Extract address and memo from details
                address = ""
                memo = ""
                for detail in contact.get("details", []):
                    if detail["key"] == "address":
                        address = detail["value"]
                    elif detail["key"] in ["destination_tag", "addressTag", "memo"]:
                        memo = detail["value"]
                
                # Map taxonomy back to our format
                taxonomy = contact.get("taxonomy", {})
                coin = taxonomy.get("currency", "").upper()
                chain_type = self._map_bitso_to_standard_network(taxonomy, coin)
                
                contacts.append(Address(
                    remark=contact.get("alias", ""),
                    coin=coin,
                    chain_type=chain_type,
                    address=address,
                    memo=memo or ""
                ))
                
            return contacts
            
        except Exception as e:
            log.error(f"Error getting contacts: {e}")
            return []

    def _map_bitso_to_standard_network(self, taxonomy: Dict, coin: str) -> str:
        """Map Bitso taxonomy back to standard network names"""
        chain = taxonomy.get("chain", "")
        network = taxonomy.get("network", "")
        protocol = taxonomy.get("protocol", "")

        # Handle USDC special cases
        if coin.upper() == "USD" and network == "circle":
            # For Circle Transfer, use the chain field directly
            if chain:
                return chain.upper()
            # Fallback: Reverse lookup from USDC_CHAIN_MAPPING
            for standard_name, bitso_chain in USDC_CHAIN_MAPPING.items():
                if bitso_chain == chain:
                    return standard_name
            return "ETH"  # Default to ETH

        # Handle other multi-network coins
        if coin.upper() == "USDT":
            if network == "tron" or protocol == "trc20":
                return "TRON"
            elif network == "ethereum" or protocol == "erc20":
                return "ETH"

        # Standard mappings based on network/protocol
        if network == "btc" and protocol == "btc":
            return "BTC"
        elif network == "eth" and protocol == "eth":
            return "ETH"
        elif network == "rp" and (protocol == "rp" or protocol == "ripple"):
            return "XRP"
        elif network == "arbitrum":
            return "ARBITRUM"
        elif network == "optimism":
            return "OPTIMISM"
        elif network == "polygon":
            return "POLYGON"
        elif network == "solana":
            return "SOL"
            
        # New mappings for recently fixed currencies
        elif network == "hedera":
            return "HBAR" 
        elif network == "stellar":
            return "XLM"
        elif network == "cosmos":
            return "ATOM"
        elif network == "near":
            return "NEAR"
        elif network == "bch":
            return "BCH"

        # Fallback
        return chain or network or coin.upper()



    def _create_contact_payload(self, address: Address) -> Dict:
        """Create contact payload for Bitso API"""
        # Extract VASP name from remark (split on first _ and take first part only)
        vasp_name = address.remark.split('_')[0]

        # Discover taxonomy procedurally from Bitso's API
        taxonomy = self._discover_taxonomy_for_address(address)

        # Build details
        details = {"address": address.address}

        # Add chain for USDC and other multi-network assets
        if taxonomy.get("network") == "circle" and "chain" in taxonomy:
            details["chain"] = taxonomy["chain"]

        # Add memo/destination_tag if present
        if address.memo:
            if address.coin.upper() == "XRP":
                try:
                    details["destination_tag"] = int(address.memo)
                except ValueError:
                    log.warning(f"Invalid XRP destination tag: {address.memo}")
                    details["destination_tag"] = address.memo
            elif taxonomy.get("chain") == "xlm":  # Stellar
                details["destination_tag"] = address.memo
            else:
                details["memo"] = address.memo

        payload = {
            "taxonomy": taxonomy,
            "details": details,
            "alias": address.remark,
            "compliance": {
                "travel_rule": {
                    "beneficiary": {
                        "name": self.beneficiary_name,
                        "type": self.beneficiary_type
                    }
                }
            }
        }

        # Add VASP information (always use name only)
        payload["compliance"]["travel_rule"]["vasp"] = {"name": vasp_name}

        return payload

    def _is_usdc_transaction(self, address: Address) -> bool:
        """Check if this is a USDC transaction"""
        return address.coin.upper() in ["USDC", "USD"]

    def _check_duplicate(self, address: Address, existing_contacts: List[Address]) -> bool:
        """Check if address already exists in contacts"""
        for contact in existing_contacts:
            # Check if addresses match
            if contact.address.lower() != address.address.lower():
                continue
                
            # Check if memos match
            if contact.memo != address.memo:
                continue
                
            # Check coin compatibility (handle USDC -> USD mapping)
            coin_match = False
            if contact.coin.upper() == address.coin.upper():
                coin_match = True
            elif (contact.coin.upper() == "USD" and address.coin.upper() == "USDC") or \
                 (contact.coin.upper() == "USDC" and address.coin.upper() == "USD"):
                coin_match = True
                
            if not coin_match:
                continue
                
            # Check chain/network compatibility
            chain_match = False
            
            # Direct match first
            if contact.chain_type.upper() == address.chain_type.upper():
                chain_match = True
            else:
                # Check if the contact's chain_type can be mapped back to match the CSV chain_type
                # This handles cases where Bitso stores different chain names than what we send
                
                # For coin-named chains (HBAR->HBAR, XRP->XRP, etc.), check if they match the coin
                if (contact.chain_type.upper() == contact.coin.upper() and 
                    address.chain_type.upper() == address.coin.upper() and
                    contact.coin.upper() == address.coin.upper()):
                    chain_match = True
                
                # For USD/USDC, they should match on the same chain
                elif ((contact.coin.upper() == "USD" and address.coin.upper() == "USDC") or
                      (contact.coin.upper() == "USDC" and address.coin.upper() == "USD")) and \
                      contact.chain_type.upper() == address.chain_type.upper():
                    chain_match = True
                      
            if chain_match:
                return True
                
        return False

    def _validate_address(self, address: Address) -> bool:
        """Validate address format and requirements"""
        if not address.address:
            log.error(f"Empty address for {address.remark}")
            return False

        if not address.coin:
            log.error(f"Empty coin for {address.remark}")
            return False

        if not address.chain_type:
            log.error(f"Empty chain_type for {address.remark}")
            return False

        # Validate XRP destination tag
        if address.coin.upper() == "XRP" and address.memo:
            try:
                int(address.memo)
            except ValueError:
                log.error(f"Invalid XRP destination tag for {address.remark}: {address.memo}")
                return False

        return True

    def whitelist_addresses(self, addresses: List[Address], dry_run: bool = True):
        """Whitelist addresses in Bitso"""
        log.info(f"Starting whitelisting process for {len(addresses)} addresses (dry_run={dry_run})")

        # Validate addresses first
        valid_addresses = []
        for addr in addresses:
            if self._validate_address(addr):
                valid_addresses.append(addr)
            else:
                log.warning(f"Skipping invalid address: {addr.remark}")

        log.info(f"Validated {len(valid_addresses)} out of {len(addresses)} addresses")

        # Get existing contacts to check for duplicates
        existing_contacts = self.get_contacts()
        log.info(f"Found {len(existing_contacts)} existing contacts")

        # Filter out duplicates
        new_addresses = []
        for addr in valid_addresses:
            if self._check_duplicate(addr, existing_contacts):
                log.info(f"Address {addr.address} ({addr.coin}/{addr.chain_type}) already exists, skipping")
                continue
            new_addresses.append(addr)

        log.info(f"Processing {len(new_addresses)} new addresses")

        success_count = 0
        error_count = 0

        for addr in new_addresses:
            try:
                payload = self._create_contact_payload(addr)
                log.info(f"Creating contact for {addr.remark}: {addr.address} ({addr.coin}/{addr.chain_type})")
                log.debug(f"Payload: {json.dumps(payload, indent=2)}")

                if not dry_run:
                    response = self._make_authenticated_request("POST", "/consumer-contacts", payload)
                    log.debug(f"Create contact response: {response.text}")

                    if response.status_code == 200:
                        data = response.json()
                        if data.get("success", False):
                            contact_id = data["payload"]["contact_id"]
                            log.info(f"Successfully created contact {contact_id} for {addr.remark}")
                            success_count += 1
                        else:
                            log.error(f"Failed to create contact for {addr.remark}: {data}")
                            error_count += 1
                    else:
                        log.error(f"HTTP error creating contact for {addr.remark}: {response.status_code} - {response.text}")
                        error_count += 1
                else:
                    log.info(f"DRY RUN: Would create contact for {addr.remark}")
                    success_count += 1

                time.sleep(0.5)  # Rate limiting

            except Exception as e:
                log.error(f"Error creating contact for {addr.remark}: {e}")
                error_count += 1

        log.info(f"Whitelisting completed: {success_count} successful, {error_count} errors")


def parse_arguments():
    parser = ArgumentParser(__file__)
    parser.add_argument("-c", "--config", required=True, help="Config file path", dest="config_file")
    parser.add_argument("--dump-current-whitelist", action="store_true", default=False)
    parser.add_argument("--dry-run", action="store_true", default=True, help="Run in dry-run mode (default: True)")
    parser.add_argument("--live", action="store_true", default=False, help="Run in live mode (disables dry-run)")
    parser.add_argument("address_file", nargs="?")
    
    return parser.parse_args()


def load_config(config_file: str) -> Dict:
    """Load configuration from YAML file"""
    with open(config_file, 'r') as f:
        return yaml.safe_load(f)


def test_taxonomies():
    """Test different taxonomies to see what Bitso accepts"""
    parser = ArgumentParser(__file__)
    parser.add_argument("-c", "--config", required=True, help="Config file path", dest="config_file")
    args = parser.parse_args()

    # Set up logging
    log.setLevel(logging.DEBUG)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    log.addHandler(handler)

    config = load_config(args.config_file)
    bitso = BitsoRequester(config)

    # Test getting withdrawal methods for different currencies
    currencies = ["btc", "eth", "xrp", "usd"]
    for currency in currencies:
        log.info(f"Getting withdrawal methods for {currency}")
        methods = bitso.get_withdrawal_methods(currency)
        log.info(f"{currency} methods: {methods}")


def main():
    args = parse_arguments()

    # Set up logging
    log.setLevel(logging.DEBUG)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    log.addHandler(handler)

    # Load configuration
    config = load_config(args.config_file)

    # Determine dry-run mode
    dry_run = not args.live if args.live else args.dry_run

    bitso = BitsoRequester(config)
    
    if args.dump_current_whitelist:
        if not args.address_file:
            log.error("Address file required for dumping whitelist")
            return
            
        with open(args.address_file, "w") as wl_file:
            contacts = bitso.get_contacts()
            wl_writer = writer(wl_file)
            wl_writer.writerow(("coin", "chain_type", "address", "memo", "remark"))
            for contact in contacts:
                wl_writer.writerow((
                    contact.coin,
                    contact.chain_type,
                    contact.address,
                    contact.memo,
                    contact.remark,
                ))
            log.info(f"Dumped {len(contacts)} contacts to {args.address_file}")
    else:
        if not args.address_file:
            log.error("Address file required for whitelisting")
            return
            
        with open(args.address_file, "r") as wl_file:
            wl_reader = DictReader(wl_file)
            addresses = []
            for row in wl_reader:
                addresses.append(Address(
                    coin=row["coin"],
                    chain_type=row["chain_type"],
                    address=row["address"],
                    memo=row["memo"],
                    remark=row["remark"],
                ))
            
            log.info(f"Loaded {len(addresses)} addresses from {args.address_file}")
            bitso.whitelist_addresses(addresses, dry_run=dry_run)


if __name__ == "__main__":
    main()
