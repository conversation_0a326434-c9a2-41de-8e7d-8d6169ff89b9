import logging
import sys
import json
import requests
import time
import hmac
import hashlib
import base64
from typing import Dict, Optional, List
from dataclasses import dataclass
from argparse import Argument<PERSON>arser
from csv import writer, Di<PERSON><PERSON><PERSON>er
try:
    import yaml
except ImportError:
    # Fallback to json if yaml is not available
    import json as yaml
    yaml.safe_load = yaml.load


log = logging.getLogger()


@dataclass
class Address:
    remark: str
    coin: str
    chain_type: str
    address: str
    memo: str


# Network mapping for USDC special cases based on Bitso documentation
USDC_NETWORK_MAP = {
    "ARBITRUM": {"chain": "ARB", "network": "circle", "protocol": "usdc_trf", "asset": "usdc", "currency": "usd", "integration": "circle-api"},
    "OPTIMISM": {"chain": "OP", "network": "circle", "protocol": "usdc_trf", "asset": "usdc", "currency": "usd", "integration": "circle-api"},
    "ETH": {"chain": "ETH", "network": "circle", "protocol": "usdc_trf", "asset": "usdc", "currency": "usd", "integration": "circle-api"},
    "ETHEREUM": {"chain": "ETH", "network": "circle", "protocol": "usdc_trf", "asset": "usdc", "currency": "usd", "integration": "circle-api"},
    "POLYGON": {"chain": "POLY", "network": "circle", "protocol": "usdc_trf", "asset": "usdc", "currency": "usd", "integration": "circle-api"},
    "STELLAR": {"chain": "xlm", "network": "circle", "protocol": "usdc_trf", "asset": "usdc", "currency": "usd", "integration": "circle-api"},
    "SOLANA": {"chain": "SOL", "network": "circle", "protocol": "usdc_trf", "asset": "usdc", "currency": "usd", "integration": "circle-api"},
}

# Standard network mapping for other coins
STANDARD_NETWORK_MAP = {
    "BTC": {"network": "btc", "protocol": "bitcoin", "asset": "btc", "currency": "btc", "integration": "fblocks-v1"},
    "ETH": {"network": "ethereum", "protocol": "ethereum", "asset": "eth", "currency": "eth", "integration": "fblocks-v1"},
    "XRP": {"network": "rp", "protocol": "ripple", "asset": "xrp", "currency": "xrp", "integration": "rippled"},
    "USDT": {"network": "tron", "protocol": "trc20", "asset": "usdt", "currency": "usdt", "integration": "fblocks-v1"},
}


class BitsoRequester:
    def __init__(self, config: Dict):
        self.base_url = config["base_url"]
        self.api_key = config["api_key"]
        self.api_secret = config["api_secret"]
        self.beneficiary_name = config["beneficiary_name"]
        self.beneficiary_type = config["beneficiary_type"]
        
        self.default_headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "user-agent": "BitsoWhitelister/1.0",
        }

    def _create_signature(self, nonce: str, method: str, request_path: str, body: str = "") -> str:
        """Create HMAC signature for Bitso API authentication"""
        message = nonce + method + request_path + body
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return signature

    def _make_authenticated_request(self, method: str, endpoint: str, data: Dict = None) -> requests.Response:
        """Make authenticated request to Bitso API"""
        nonce = str(int(time.time() * 1000))
        request_path = f"/api/v3{endpoint}"
        body = json.dumps(data) if data else ""
        
        signature = self._create_signature(nonce, method, request_path, body)
        
        headers = self.default_headers.copy()
        headers.update({
            "Authorization": f"Bitso {self.api_key}:{nonce}:{signature}",
        })
        
        url = f"{self.base_url}{request_path}"
        
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, data=body)
        else:
            raise ValueError(f"Unsupported method: {method}")
            
        return response

    def get_contacts(self) -> List[Address]:
        """Get existing contacts/whitelist from Bitso"""
        try:
            response = self._make_authenticated_request("GET", "/consumer-contacts")
            log.debug(f"Get contacts response: {response.text}")
            
            if response.status_code != 200:
                log.error(f"Failed to get contacts: {response.status_code} - {response.text}")
                return []
                
            data = response.json()
            if not data.get("success", False):
                log.error(f"API returned error: {data}")
                return []
                
            contacts = []
            for contact in data.get("payload", []):
                # Extract address and memo from details
                address = ""
                memo = ""
                for detail in contact.get("details", []):
                    if detail["key"] == "address":
                        address = detail["value"]
                    elif detail["key"] in ["destination_tag", "addressTag"]:
                        memo = detail["value"]
                
                # Map taxonomy back to our format
                taxonomy = contact.get("taxonomy", {})
                coin = taxonomy.get("currency", "").upper()
                chain_type = self._map_bitso_to_standard_network(taxonomy, coin)
                
                contacts.append(Address(
                    remark=contact.get("alias", ""),
                    coin=coin,
                    chain_type=chain_type,
                    address=address,
                    memo=memo or ""
                ))
                
            return contacts
            
        except Exception as e:
            log.error(f"Error getting contacts: {e}")
            return []

    def _map_bitso_to_standard_network(self, taxonomy: Dict, coin: str) -> str:
        """Map Bitso taxonomy back to standard network names"""
        chain = taxonomy.get("chain", "")
        network = taxonomy.get("network", "")
        protocol = taxonomy.get("protocol", "")

        # Handle USDC special cases
        if coin.upper() == "USD" and network == "circle":
            chain_mapping = {
                "ARB": "ARBITRUM",
                "OP": "OPTIMISM",
                "ETH": "ETH",
                "POLY": "POLYGON",
                "xlm": "STELLAR",
                "SOL": "SOLANA"
            }
            return chain_mapping.get(chain, chain)

        # Handle other multi-network coins
        if coin.upper() == "USDT":
            if network == "tron" or protocol == "trc20":
                return "TRON"
            elif network == "ethereum" or protocol == "erc20":
                return "ETH"

        # Standard mappings
        coin_mapping = {
            "BTC": "BTC",
            "ETH": "ETH",
            "XRP": "XRP",
            "USDT": "TRON"  # Default USDT to TRON
        }

        return coin_mapping.get(coin.upper(), chain or network or coin)

    def _get_vasp_did(self, vasp_name: str) -> Optional[str]:
        """Get VASP DID from Bitso's catalog"""
        try:
            response = self._make_authenticated_request("GET", "/vasps")
            log.debug(f"Get VASPs response: {response.text}")

            if response.status_code != 200:
                log.warning(f"Failed to get VASPs catalog: {response.status_code}")
                return None

            data = response.json()
            if not data.get("success", False):
                log.warning(f"VASPs API returned error: {data}")
                return None

            # Handle both list and dict payload formats
            payload = data.get("payload", [])
            if isinstance(payload, dict):
                payload = payload.get("vasps", [])

            # Search for VASP by name
            for vasp in payload:
                if isinstance(vasp, dict) and vasp.get("name", "").lower() == vasp_name.lower():
                    return vasp.get("did")

            log.warning(f"VASP '{vasp_name}' not found in catalog")
            return None

        except Exception as e:
            log.warning(f"Error getting VASP DID: {e}")
            return None

    def _create_contact_payload(self, address: Address) -> Dict:
        """Create contact payload for Bitso API"""
        # Extract VASP name from remark (split on last _ and take first half)
        remark_parts = address.remark.rsplit('_', 1)
        vasp_name = remark_parts[0] if len(remark_parts) > 1 else address.remark

        # Get VASP DID
        vasp_did = self._get_vasp_did(vasp_name)

        # Determine taxonomy based on coin and chain
        # Special handling for USDC across different networks
        if self._is_usdc_transaction(address):
            if address.chain_type.upper() not in USDC_NETWORK_MAP:
                log.warning(f"Unsupported USDC network: {address.chain_type}")
                # Default to ETH for unknown USDC networks
                taxonomy = USDC_NETWORK_MAP["ETH"].copy()
            else:
                taxonomy = USDC_NETWORK_MAP[address.chain_type.upper()].copy()
        else:
            taxonomy = STANDARD_NETWORK_MAP.get(address.coin.upper(), {
                "network": address.chain_type.lower(),
                "protocol": address.chain_type.lower(),
                "asset": address.coin.lower(),
                "currency": address.coin.lower()
            })

        # Build details
        details = {"address": address.address}

        # Add chain for USDC and other multi-network assets
        if self._is_usdc_transaction(address) or taxonomy.get("network") == "circle":
            details["chain"] = taxonomy.get("chain", address.chain_type)

        # Add memo/destination_tag if present
        if address.memo:
            if address.coin.upper() == "XRP":
                try:
                    details["destination_tag"] = int(address.memo)
                except ValueError:
                    log.warning(f"Invalid XRP destination tag: {address.memo}")
                    details["destination_tag"] = address.memo
            elif taxonomy.get("chain") == "xlm":  # Stellar
                details["destination_tag"] = address.memo
            else:
                details["memo"] = address.memo

        payload = {
            "taxonomy": taxonomy,
            "details": details,
            "alias": address.remark,
            "compliance": {
                "travel_rule": {
                    "beneficiary": {
                        "name": self.beneficiary_name,
                        "type": self.beneficiary_type
                    }
                }
            }
        }

        # Add VASP information
        if vasp_did:
            payload["compliance"]["travel_rule"]["vasp"] = {"did": vasp_did}
        else:
            payload["compliance"]["travel_rule"]["vasp"] = {"name": vasp_name}

        return payload

    def _is_usdc_transaction(self, address: Address) -> bool:
        """Check if this is a USDC transaction"""
        return (address.coin.upper() in ["USDC", "USD"] and
                address.chain_type.upper() in USDC_NETWORK_MAP)

    def _check_duplicate(self, address: Address, existing_contacts: List[Address]) -> bool:
        """Check if address already exists in contacts"""
        for contact in existing_contacts:
            if (contact.address.lower() == address.address.lower() and
                contact.coin.upper() == address.coin.upper() and
                contact.chain_type.upper() == address.chain_type.upper() and
                contact.memo == address.memo):
                return True
        return False

    def _validate_address(self, address: Address) -> bool:
        """Validate address format and requirements"""
        if not address.address:
            log.error(f"Empty address for {address.remark}")
            return False

        if not address.coin:
            log.error(f"Empty coin for {address.remark}")
            return False

        if not address.chain_type:
            log.error(f"Empty chain_type for {address.remark}")
            return False

        # Validate XRP destination tag
        if address.coin.upper() == "XRP" and address.memo:
            try:
                int(address.memo)
            except ValueError:
                log.error(f"Invalid XRP destination tag for {address.remark}: {address.memo}")
                return False

        return True

    def whitelist_addresses(self, addresses: List[Address], dry_run: bool = True):
        """Whitelist addresses in Bitso"""
        log.info(f"Starting whitelisting process for {len(addresses)} addresses (dry_run={dry_run})")

        # Validate addresses first
        valid_addresses = []
        for addr in addresses:
            if self._validate_address(addr):
                valid_addresses.append(addr)
            else:
                log.warning(f"Skipping invalid address: {addr.remark}")

        log.info(f"Validated {len(valid_addresses)} out of {len(addresses)} addresses")

        # Get existing contacts to check for duplicates
        existing_contacts = self.get_contacts()
        log.info(f"Found {len(existing_contacts)} existing contacts")

        # Filter out duplicates
        new_addresses = []
        for addr in valid_addresses:
            if self._check_duplicate(addr, existing_contacts):
                log.info(f"Address {addr.address} ({addr.coin}/{addr.chain_type}) already exists, skipping")
                continue
            new_addresses.append(addr)

        log.info(f"Processing {len(new_addresses)} new addresses")

        success_count = 0
        error_count = 0

        for addr in new_addresses:
            try:
                payload = self._create_contact_payload(addr)
                log.info(f"Creating contact for {addr.remark}: {addr.address} ({addr.coin}/{addr.chain_type})")
                log.debug(f"Payload: {json.dumps(payload, indent=2)}")

                if not dry_run:
                    response = self._make_authenticated_request("POST", "/consumer-contacts", payload)
                    log.debug(f"Create contact response: {response.text}")

                    if response.status_code == 200:
                        data = response.json()
                        if data.get("success", False):
                            contact_id = data["payload"]["contact_id"]
                            log.info(f"Successfully created contact {contact_id} for {addr.remark}")
                            success_count += 1
                        else:
                            log.error(f"Failed to create contact for {addr.remark}: {data}")
                            error_count += 1
                    else:
                        log.error(f"HTTP error creating contact for {addr.remark}: {response.status_code} - {response.text}")
                        error_count += 1
                else:
                    log.info(f"DRY RUN: Would create contact for {addr.remark}")
                    success_count += 1

                time.sleep(0.5)  # Rate limiting

            except Exception as e:
                log.error(f"Error creating contact for {addr.remark}: {e}")
                error_count += 1

        log.info(f"Whitelisting completed: {success_count} successful, {error_count} errors")


def parse_arguments():
    parser = ArgumentParser(__file__)
    parser.add_argument("-c", "--config", required=True, help="Config file path", dest="config_file")
    parser.add_argument("--dump-current-whitelist", action="store_true", default=False)
    parser.add_argument("--dry-run", action="store_true", default=True, help="Run in dry-run mode (default: True)")
    parser.add_argument("--live", action="store_true", default=False, help="Run in live mode (disables dry-run)")
    parser.add_argument("address_file", nargs="?")
    
    return parser.parse_args()


def load_config(config_file: str) -> Dict:
    """Load configuration from YAML file"""
    with open(config_file, 'r') as f:
        return yaml.safe_load(f)


def main():
    args = parse_arguments()
    
    # Set up logging
    log.setLevel(logging.DEBUG)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    log.addHandler(handler)
    
    # Load configuration
    config = load_config(args.config_file)
    
    # Determine dry-run mode
    dry_run = not args.live if args.live else args.dry_run
    
    bitso = BitsoRequester(config)
    
    if args.dump_current_whitelist:
        if not args.address_file:
            log.error("Address file required for dumping whitelist")
            return
            
        with open(args.address_file, "w") as wl_file:
            contacts = bitso.get_contacts()
            wl_writer = writer(wl_file)
            wl_writer.writerow(("coin", "chain_type", "address", "memo", "remark"))
            for contact in contacts:
                wl_writer.writerow((
                    contact.coin,
                    contact.chain_type,
                    contact.address,
                    contact.memo,
                    contact.remark,
                ))
            log.info(f"Dumped {len(contacts)} contacts to {args.address_file}")
    else:
        if not args.address_file:
            log.error("Address file required for whitelisting")
            return
            
        with open(args.address_file, "r") as wl_file:
            wl_reader = DictReader(wl_file)
            addresses = []
            for row in wl_reader:
                addresses.append(Address(
                    coin=row["coin"],
                    chain_type=row["chain_type"],
                    address=row["address"],
                    memo=row["memo"],
                    remark=row["remark"],
                ))
            
            log.info(f"Loaded {len(addresses)} addresses from {args.address_file}")
            bitso.whitelist_addresses(addresses, dry_run=dry_run)


if __name__ == "__main__":
    main()
