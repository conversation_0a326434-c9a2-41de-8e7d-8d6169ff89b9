#!/usr/bin/env python3
"""
Focus Area Analysis Module for Transaction Fee Analyzer.

This module contains advanced statistical analysis functions for identifying
high-impact fee optimization opportunities.
"""

import pandas as pd
import numpy as np
from .db import get_transfer_fee_analysis



def calculate_advanced_fee_analysis(transfer_fee_df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate advanced fee analysis including z-scores, log fees, scoring, and potential savings.
        
    Returns:
        pd.DataFrame: Enhanced analysis with additional columns:
            - network_mean_fee: Mean average fee for the network across all exchanges
            - network_stdev_fee: Standard deviation of fees for the network
            - z_score: Z-score for the exchange and network combination
            - log_total_fee: Natural logarithm of total fees
            - scoring: Z-score multiplied by log of total fees
            - potential_savings: Savings if fee was network average
            - meets_criteria: Boolean filter (scoring > 1 AND savings > 1000)
    """ 

    # Create a copy to avoid modifying the original DataFrame
    analysis_df = transfer_fee_df.copy()

    # calculate average one transfer fee for each row
    analysis_df['avg_fee_usdt'] = analysis_df['total_fee_usdt'] / analysis_df['transfer_count']

    # Calculate network-level statistics (mean (devide the sum of all fees in the network by total numbers of transfers in the network) and standard deviation of avg_fee_usdt by network)
    network_stats = analysis_df.groupby('network').agg({
        'total_fee_usdt': 'sum',
        'transfer_count': 'sum'
    }).reset_index()
    network_stats['network_mean_fee'] = network_stats['total_fee_usdt'] / network_stats['transfer_count']
    network_stats['network_stdev_fee'] = analysis_df.groupby('network')['avg_fee_usdt'].std().reset_index()['avg_fee_usdt']
    network_stats = network_stats[['network', 'network_mean_fee', 'network_stdev_fee']]
    network_stats.columns = ['network', 'network_mean_fee', 'network_stdev_fee']
    
    # Handle cases where standard deviation is 0 or NaN (only one exchange for a network)
    network_stats['network_stdev_fee'] = network_stats['network_stdev_fee'].fillna(1.0)
    network_stats.loc[network_stats['network_stdev_fee'] == 0, 'network_stdev_fee'] = 1.0
    
    # Merge network statistics back to the main DataFrame
    analysis_df = analysis_df.merge(network_stats, on='network', how='left')
    
    # Calculate Z-score for each exchange-network combination
    analysis_df['z_score'] = (
        (analysis_df['avg_fee_usdt'] - analysis_df['network_mean_fee']) / 
        analysis_df['network_stdev_fee']
    )
    
    # Calculate log10 of total fees (handle zero or negative values)
    analysis_df['log_total_fee'] = np.log10(np.maximum(analysis_df['total_fee_usdt'], 0.01))
    
    # Calculate scoring as z_score * log_total_fee
    analysis_df['scoring'] = analysis_df['z_score'] * analysis_df['log_total_fee']
    
    # Calculate potential savings if fee was network average
    analysis_df['potential_savings'] = (
        (analysis_df['avg_fee_usdt'] - analysis_df['network_mean_fee']) * 
        analysis_df['transfer_count']
    )
    
    # Apply filter criteria: scoring > 1 AND potential_savings > 1000
    analysis_df['meets_criteria'] = (
        (analysis_df['scoring'] > 1) & 
        (analysis_df['potential_savings'] > 1000)
    )
    
    return analysis_df


def get_filtered_high_impact_transfers(df: pd.DataFrame) -> pd.DataFrame:
    """
    Filter transfer fee analysis to show only high-impact optimization opportunities.

    Args:
        df: DataFrame with enhanced analysis (must include 'meets_criteria' column).

    Returns:
        pd.DataFrame: Filtered results where scoring > 1 and potential savings > 1000,
                     sorted by potential savings (descending)
    """
    # Filter based on criteria
    filtered_df = df[df['meets_criteria']].copy()

    # Sort by potential savings (descending)
    filtered_df = filtered_df.sort_values('potential_savings', ascending=False)

    return filtered_df
