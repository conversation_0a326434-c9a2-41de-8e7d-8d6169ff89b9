# Bitso Whitelister

A Python tool for managing cryptocurrency address whitelisting on Bitso exchange via their API.

## Features

- **Multi-network USDC support**: Handles USDC across Ethereum, Arbitrum, Optimism, Polygon, Stellar, and Solana networks
- **Travel Rule compliance**: Automatically includes compliance information for regulatory requirements
- **Duplicate detection**: Checks existing contacts to avoid duplicate entries
- **Dry-run mode**: Test functionality without making actual API calls
- **VASP DID lookup**: Automatically retrieves VASP DIDs from <PERSON><PERSON>'s catalog
- **Comprehensive validation**: Validates addresses and memos before submission

## Requirements

- Python 3.6+
- `requests` library
- `pyyaml` library (optional, will fallback to JSON if not available)

## Configuration

Create a `config.yml` file with your Bitso API credentials:

```yaml
base_url: "https://bitso.com"  # Use "https://stage.bitso.com" for testing
api_key: "your_api_key"
api_secret: "your_api_secret"
beneficiary_name: "Your Company Inc."
beneficiary_type: "LEGAL"  # or "NATURAL" for individuals
```

## Usage

### Basic Usage

```bash
# Dry run (default - no actual API calls)
python bitso_whitelister.py -c config.yml addresses.csv

# Live mode (makes actual API calls)
python bitso_whitelister.py -c config.yml --live addresses.csv

# Dump current whitelist
python bitso_whitelister.py -c config.yml --dump-current-whitelist current_whitelist.csv
```

### CSV Format

The input CSV file should have the following columns:

```csv
coin,chain_type,address,memo,remark
BTC,BTC,*********************************,,binance_BTC
ETH,ETH,******************************************,,binance_ETH
USDC,ARBITRUM,******************************************,,binance_USDC
XRP,XRP,rNxp4h8apvRis6mJf9Sh8C6iRxfrDWN7AV,*********,binance_XRP
```

### Supported Networks

#### Standard Cryptocurrencies
- **BTC**: Bitcoin network
- **ETH**: Ethereum network  
- **XRP**: Ripple network (supports destination tags)
- **USDT**: Tron network (TRC20)

#### USDC Multi-Network Support
- **ETH**: Ethereum
- **ARBITRUM**: Arbitrum
- **OPTIMISM**: Optimism
- **POLYGON**: Polygon
- **STELLAR**: Stellar (supports memos)
- **SOLANA**: Solana

## VASP Name Extraction

The tool extracts VASP names from the `remark` field by splitting on the last underscore and taking the first part:

- `binance_BTC` → VASP name: `binance`
- `kraken_ETH_test` → VASP name: `kraken_ETH`

## Travel Rule Compliance

The tool automatically includes Travel Rule compliance information:

- **Beneficiary name**: From config file
- **Beneficiary type**: From config file (LEGAL/NATURAL)
- **VASP DID**: Automatically looked up from Bitso's VASP catalog
- **VASP name**: Used as fallback if DID not found

## Error Handling

The tool includes comprehensive error handling:

- **Address validation**: Checks for empty fields and invalid formats
- **XRP memo validation**: Ensures destination tags are numeric
- **Duplicate detection**: Prevents re-adding existing addresses
- **API error handling**: Logs detailed error messages
- **Rate limiting**: Includes delays between API calls

## Testing

Run the test suite to verify functionality:

```bash
python test_bitso_whitelister.py
```

The tests cover:
- Payload creation for different address types
- Address validation
- Duplicate detection
- Dry-run functionality

## Examples

### Example 1: Basic Whitelisting

```bash
# Create addresses.csv with your addresses
python bitso_whitelister.py -c config.yml --dry-run addresses.csv

# Review the output, then run live
python bitso_whitelister.py -c config.yml --live addresses.csv
```

### Example 2: USDC Multi-Network

```csv
coin,chain_type,address,memo,remark
USDC,ETH,0x1234...,,exchange_USDC_ETH
USDC,ARBITRUM,0x1234...,,exchange_USDC_ARB
USDC,POLYGON,0x1234...,,exchange_USDC_POLY
USDC,STELLAR,GBBD47IF6LWK7P7MDEVSCWR7DPUWV3NY3DTQEVFL4NAT4AQH3ZLLFLA5,memo123,exchange_USDC_XLM
```

### Example 3: Dump Current Whitelist

```bash
python bitso_whitelister.py -c config.yml --dump-current-whitelist current.csv
```

## API Endpoints Used

- `GET /api/v3/consumer-contacts` - Retrieve existing contacts
- `POST /api/v3/consumer-contacts` - Create new contacts
- `GET /api/v3/vasps` - Get VASP DID catalog

## Security Notes

- Store API credentials securely
- Use staging environment for testing
- Always run in dry-run mode first
- Monitor API rate limits
- Keep logs for audit purposes

## Troubleshooting

### Common Issues

1. **Invalid API credentials**: Check your API key and secret
2. **Network not supported**: Verify the chain_type is supported
3. **Invalid XRP memo**: Ensure destination tags are numeric
4. **VASP not found**: Check VASP name extraction from remark
5. **Rate limiting**: Increase delays between API calls

### Debug Mode

Enable debug logging by modifying the log level in the script:

```python
log.setLevel(logging.DEBUG)
```

This will show detailed API requests and responses.
