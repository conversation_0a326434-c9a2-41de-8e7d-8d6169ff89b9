#!/usr/bin/env python3
"""
Test script for Bitso whitelister
"""

import sys
import os
import tempfile
import csv
from bitso_whitelister import BitsoRequester, Address, load_config

def create_test_config():
    """Create a test config file"""
    config = {
        "base_url": "https://stage.bitso.com",  # Use staging for testing
        "api_key": "test_key",
        "api_secret": "test_secret", 
        "beneficiary_name": "Test Company Inc.",
        "beneficiary_type": "LEGAL"
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
        # Write as simple key-value pairs since yaml might not be available
        for key, value in config.items():
            f.write(f'{key}: "{value}"\n')
        return f.name

def create_test_addresses():
    """Create test addresses CSV file"""
    test_addresses = [
        {
            "coin": "BTC",
            "chain_type": "BTC", 
            "address": "*********************************",
            "memo": "",
            "remark": "binance_BTC"
        },
        {
            "coin": "ETH",
            "chain_type": "ETH",
            "address": "******************************************", 
            "memo": "",
            "remark": "binance_ETH"
        },
        {
            "coin": "USDC",
            "chain_type": "ARBITRUM",
            "address": "******************************************",
            "memo": "",
            "remark": "binance_USDC_ARB"
        },
        {
            "coin": "XRP", 
            "chain_type": "XRP",
            "address": "rNxp4h8apvRis6mJf9Sh8C6iRxfrDWN7AV",
            "memo": "*********",
            "remark": "binance_XRP"
        }
    ]
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, newline='') as f:
        writer = csv.DictWriter(f, fieldnames=["coin", "chain_type", "address", "memo", "remark"])
        writer.writeheader()
        writer.writerows(test_addresses)
        return f.name

def test_payload_creation():
    """Test payload creation for different address types"""
    print("Testing payload creation...")
    
    config_file = create_test_config()
    try:
        config = load_config(config_file)
        bitso = BitsoRequester(config)
        
        # Test BTC address
        btc_addr = Address(
            remark="binance_BTC",
            coin="BTC", 
            chain_type="BTC",
            address="*********************************",
            memo=""
        )
        
        payload = bitso._create_contact_payload(btc_addr)
        print(f"BTC Payload: {payload}")
        assert payload["taxonomy"]["currency"] == "btc"
        assert payload["alias"] == "binance_BTC"
        assert payload["compliance"]["travel_rule"]["vasp"]["name"] == "binance"
        
        # Test USDC address
        usdc_addr = Address(
            remark="binance_USDC_ARB",
            coin="USDC",
            chain_type="ARBITRUM", 
            address="******************************************",
            memo=""
        )
        
        payload = bitso._create_contact_payload(usdc_addr)
        print(f"USDC Payload: {payload}")
        assert payload["taxonomy"]["currency"] == "usd"
        assert payload["taxonomy"]["network"] == "circle"
        assert payload["details"]["chain"] == "ARB"
        
        # Test XRP address with memo
        xrp_addr = Address(
            remark="binance_XRP",
            coin="XRP",
            chain_type="XRP",
            address="rNxp4h8apvRis6mJf9Sh8C6iRxfrDWN7AV", 
            memo="*********"
        )
        
        payload = bitso._create_contact_payload(xrp_addr)
        print(f"XRP Payload: {payload}")
        assert payload["taxonomy"]["currency"] == "xrp"
        assert payload["details"]["destination_tag"] == *********
        
        print("✓ Payload creation tests passed!")
        
    finally:
        os.unlink(config_file)

def test_validation():
    """Test address validation"""
    print("Testing address validation...")
    
    config_file = create_test_config()
    try:
        config = load_config(config_file)
        bitso = BitsoRequester(config)
        
        # Valid address
        valid_addr = Address("test", "BTC", "BTC", "*********************************", "")
        assert bitso._validate_address(valid_addr) == True
        
        # Invalid XRP memo
        invalid_xrp = Address("test", "XRP", "XRP", "rNxp4h8apvRis6mJf9Sh8C6iRxfrDWN7AV", "invalid_memo")
        assert bitso._validate_address(invalid_xrp) == False
        
        # Empty address
        empty_addr = Address("test", "BTC", "BTC", "", "")
        assert bitso._validate_address(empty_addr) == False
        
        print("✓ Validation tests passed!")
        
    finally:
        os.unlink(config_file)

def test_duplicate_detection():
    """Test duplicate address detection"""
    print("Testing duplicate detection...")
    
    config_file = create_test_config()
    try:
        config = load_config(config_file)
        bitso = BitsoRequester(config)
        
        addr1 = Address("test1", "BTC", "BTC", "*********************************", "")
        addr2 = Address("test2", "BTC", "BTC", "*********************************", "")  # Same address
        addr3 = Address("test3", "BTC", "BTC", "1DifferentAddress", "")  # Different address
        
        existing = [addr1]
        
        assert bitso._check_duplicate(addr2, existing) == True  # Should be duplicate
        assert bitso._check_duplicate(addr3, existing) == False  # Should not be duplicate
        
        print("✓ Duplicate detection tests passed!")
        
    finally:
        os.unlink(config_file)

def test_dry_run():
    """Test dry run functionality"""
    print("Testing dry run functionality...")
    
    config_file = create_test_config()
    addresses_file = create_test_addresses()
    
    try:
        config = load_config(config_file)
        bitso = BitsoRequester(config)
        
        # Load test addresses
        with open(addresses_file, 'r') as f:
            reader = csv.DictReader(f)
            addresses = [
                Address(
                    remark=row["remark"],
                    coin=row["coin"],
                    chain_type=row["chain_type"],
                    address=row["address"],
                    memo=row["memo"]
                )
                for row in reader
            ]
        
        # Run in dry-run mode (should not make actual API calls)
        bitso.whitelist_addresses(addresses, dry_run=True)
        
        print("✓ Dry run test completed!")
        
    finally:
        os.unlink(config_file)
        os.unlink(addresses_file)

def main():
    """Run all tests"""
    print("Running Bitso whitelister tests...\n")
    
    try:
        test_payload_creation()
        print()
        test_validation()
        print()
        test_duplicate_detection()
        print()
        test_dry_run()
        print()
        print("✓ All tests passed!")
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
