from typing import Dict, Any
import requests

def send_to_slack(webhook_url: str, message: str) -> None:
    payload = {"text": message}
    try:
        response = requests.post(webhook_url, json=payload)
        if response.status_code != 200:
            print(f"Slack error: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"Error sending to Slack: {e}")

def format_message(announcement: Dict[str, Any]) -> str:
    return (
        f"*{announcement['title']}*\n"
        f"{announcement['url']}\n"
        f"*Date:* {announcement['date']}\n"
        f"Source: {announcement['source']}"
    )
