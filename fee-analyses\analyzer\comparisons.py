#!/usr/bin/env python3
"""
Exchange Comparison Module for Transaction Fee Analyzer.

This module provides functionality to compare fee structures between different exchanges
over multiple time periods.
"""

import numpy as np
import pandas as pd
from typing import List, Optional, Tuple, Dict
from .db import get_transfer_fee_analysis


def compare_exchanges(
    exchange_name: str,
    competitors: List[str],
    day_intervals: List[int] = [7, 30, 60, 90],
    asset: Optional[str] = None,
    network: Optional[str] = None,
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Build two tables comparing primary exchange vs competitors.

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: (summary_df, competitor_detail_df)
            - summary_df: Summary comparison with avg fees, counts, and % difference
            - competitor_detail_df: Per-competitor fee details across time periods
    """
    # Prepare containers
    summary: Dict[str, Dict[int, float]] = {
        f"{exchange_name} Avg Fee": {},
        "Competitors Avg Fee": {},
        "% Difference": {},
        f"{exchange_name} Transfer Count": {},
        "Competitor Transfer Count": {},
    }
    comp_details: Dict[str, Dict[int, float]] = {c: {} for c in competitors}

    for days in day_intervals:
        # 1) Pull raw data
        df = get_transfer_fee_analysis(days=days)
        
        # 2) Apply filters
        if asset:
            df = df[df["gt_asset"] == asset]
        if network:
            df = df[df["network"] == network]

        # 3) Compute exchange_name metrics
        df_p = df[df["exchange_from"] == exchange_name]
        total_fee_p = df_p["total_fee_usdt"].sum()
        cnt_p = df_p["transfer_count"].sum()
        avg_fee_p = (total_fee_p / cnt_p) if cnt_p else np.nan

        # 4) Compute each competitor's avg and then take the mean of those avgs
        avg_list: List[float] = []
        for comp in competitors:
            df_ci = df[df["exchange_from"] == comp]
            total_fee_ci = df_ci["total_fee_usdt"].sum()
            cnt_ci = df_ci["transfer_count"].sum()
            avg_ci = (total_fee_ci / cnt_ci) if cnt_ci else np.nan
            comp_details[comp][days] = avg_ci
            if not np.isnan(avg_ci):
                avg_list.append(avg_ci)

        # Simple mean of competitor averages
        avg_fee_c = np.nanmean(avg_list) if avg_list else np.nan

        # Total competitor transfer count
        cnt_c = int(df[df["exchange_from"].isin(competitors)]["transfer_count"].sum())

        # 5) Fill in summary
        summary[f"{exchange_name} Avg Fee"][days] = float(avg_fee_p)
        summary["Competitors Avg Fee"][days] = float(avg_fee_c)
        summary["% Difference"][days] = float(
            (avg_fee_p / avg_fee_c - 1) * 100 if avg_fee_c else np.nan
        )
        summary[f"{exchange_name} Transfer Count"][days] = cnt_p
        summary["Competitor Transfer Count"][days] = cnt_c

        # 6) Fill in each competitor's own avg fee
        for comp in competitors:
            df_ci = df[df["exchange_from"] == comp]
            total_fee_ci = df_ci["total_fee_usdt"].sum()
            cnt_ci = df_ci["transfer_count"].sum()
            comp_details[comp][days] = (total_fee_ci / cnt_ci) if cnt_ci else np.nan

    # 7) Build DataFrames
    summary_df = pd.DataFrame(summary).T
    summary_df.columns = day_intervals
    summary_df.index.name = ""

    comp_df = pd.DataFrame(comp_details).T
    comp_df.columns = day_intervals
    comp_df.index.name = "Competitor"

    return summary_df, comp_df