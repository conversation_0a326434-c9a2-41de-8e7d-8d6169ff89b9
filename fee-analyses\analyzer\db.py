import psycopg2
import psycopg2.extras
import yaml
import os
from typing import Dict, Any, List, Optional
import pandas as pd


class DatabaseConnection:

    def __init__(self, config_path: str = "config.local.yml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.connection = None
        self._is_connected = False

    def _load_config(self) -> Dict[str, Any]:
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"Config file not found: {self.config_path}")

        with open(self.config_path, 'r') as file:
            config = yaml.safe_load(file)

        if 'database' not in config:
            raise ValueError("Database configuration not found in config file")

        return config['database']

    def connect(self) -> psycopg2.extensions.connection:
        if self._is_connected and self.connection:
            return self.connection

        try:
            self.connection = psycopg2.connect(
                host=self.config['host'],
                port=self.config['port'],
                database=self.config['database'],
                user=self.config['user'],
                password=self.config['password']
            )
            self._is_connected = True
            print(f"Successfully connected to database: {self.config['database']}")
            return self.connection
        except psycopg2.Error as e:
            print(f"Error connecting to database: {e}")
            raise

    def disconnect(self):
        if self.connection and self._is_connected:
            self.connection.close()
            self.connection = None
            self._is_connected = False
            print("Database connection closed")

    def is_connected(self) -> bool:
        return self._is_connected and self.connection is not None

    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        if not self.is_connected():
            raise RuntimeError("Database connection not established. Call connect() first.")

        try:
            with self.connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                cursor.execute(query, params)
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except psycopg2.Error as e:
            print(f"Error executing query: {e}")
            raise

    def execute_query_to_dataframe(self, query: str, params: Optional[tuple] = None) -> pd.DataFrame:
        if not self.is_connected():
            raise RuntimeError("Database connection not established. Call connect() first.")

        try:
            return pd.read_sql_query(query, self.connection, params=params)
        except Exception as e:
            print(f"Error executing query to DataFrame: {e}")
            raise


class DatabaseManager:

    _instance = None
    _db_connection = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._db_connection is None:
            self._db_connection = DatabaseConnection()

    def get_connection(self) -> DatabaseConnection:
        if not self._db_connection.is_connected():
            self._db_connection.connect()
        return self._db_connection



# Global database manager instance
_db_manager = DatabaseManager()


def get_database_connection() -> DatabaseConnection:
    return _db_manager.get_connection()


def get_transfer_fee_analysis(days: int = 90, returned_row_limit: Optional[int] = None) -> pd.DataFrame:
    """
    Get transfer fee analysis for the specified number of days.

    Returns:
        pd.DataFrame: Analysis results with columns:
            - network: Network name
            - exchange_from: Source exchange
            - gt_asset: Asset type
            - exchange_to: Destination exchange (if included in query)
            - transfer_count: Number of transfers
            - total_fee_usdt: Total fees in USDT
            - avg_fee_usdt: Average fee in USDT (if calculated)
            - total_transfer_usdt: Total transfer amount in USDT
            - avg_transfer_usdt: Average transfer amount in USDT (if calculated)
    """
    limit_clause = f"LIMIT {returned_row_limit}" if returned_row_limit else ""

    query = f"""
    SELECT
        t.network,
        sm_from.exchange AS exchange_from,
        t.gt_asset,
        sm_to.exchange   AS exchange_to,
        COUNT(*)                              AS transfer_count,
        SUM(t.gt_fee_in_usdt)                 AS total_fee_usdt,
        AVG(t.gt_fee_in_usdt)                 AS avg_fee_usdt,
        SUM(t.gt_amount_in_usdt)              AS total_transfer_usdt,
        AVG(t.gt_amount_in_usdt)              AS avg_transfer_usdt
    FROM capman.transfers        AS t
    JOIN capman.sources_map      AS sm_from ON t.gt_source_from = sm_from.gt_source
    JOIN capman.sources_map      AS sm_to   ON t.gt_source_to   = sm_to.gt_source
    WHERE t.gt_timestamp >= (CURRENT_DATE - INTERVAL '{days + 1} days')
        AND t.gt_timestamp <  CURRENT_DATE -- Exclude current day to have the same data as in BI
    AND  t.gt_status_id   IN (200)
    GROUP BY
        t.network,
        t.gt_asset,
        sm_from.exchange,
        sm_to.exchange
    ORDER BY total_fee_usdt desc
    {limit_clause};
    """

    db = get_database_connection()
    return db.execute_query_to_dataframe(query)

