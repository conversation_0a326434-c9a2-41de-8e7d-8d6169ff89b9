import yaml
with open('config.yml', 'r') as file:
    config = yaml.safe_load(file)

def is_important_announcement(title: str) -> bool:
    title_lower = title.lower()
    keywords = config['IMPORTANT_KEYWORDS']
    for keyword in keywords:
        if len(keyword.split()) == 1:
            if keyword.lower() in title_lower:
                return True
        else:
            if all(word in title_lower for word in keyword.split()):
                return True
    return False

